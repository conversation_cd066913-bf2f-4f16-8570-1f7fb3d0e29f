{"name": "zhead", "type": "module", "version": "2.2.4", "packageManager": "pnpm@8.9.2", "description": "All the types you need for your <head>", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/harlan-zw", "homepage": "https://github.com/harlan-zw/zhead#readme", "repository": {"type": "git", "url": "git+https://github.com/harlan-zw/zhead.git"}, "bugs": {"url": "https://github.com/harlan-zw/zhead/issues"}, "keywords": ["head", "types"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./db": {"types": "./dist/db.d.ts", "import": "./dist/db.mjs", "require": "./dist/db.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "devDependencies": {"@antfu/eslint-config": "1.0.0-beta.28", "@types/fs-extra": "^11.0.3", "@vitest/ui": "^0.34.6", "bumpp": "^9.2.0", "eslint": "^8.52.0", "fs-extra": "^11.1.1", "jsdom": "^22.1.0", "openai": "^4.13.0", "typescript": "^5.2.2", "unbuild": "^2.0.0", "utility-types": "^3.10.0", "vitest": "^0.34.6"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub", "test": "vitest", "export:sizes": "npx export-size -r", "release": "bumpp package.json --commit --push --tag", "lint": "eslint \"**/*.{ts,vue,json,yml}\" --fix"}}
{"name": "uvu", "version": "0.5.6", "repository": "lukeed/uvu", "description": "<PERSON><PERSON> is an extremely fast and lightweight test runner for Node.js and the browser", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "bin": {"uvu": "bin.js"}, "exports": {".": {"types": "./index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./assert": {"types": "./assert/index.d.ts", "require": "./assert/index.js", "import": "./assert/index.mjs"}, "./diff": {"types": "./diff/index.d.ts", "require": "./diff/index.js", "import": "./diff/index.mjs"}, "./parse": {"types": "./parse/index.d.ts", "require": "./parse/index.js", "import": "./parse/index.mjs"}, "./run": {"types": "./run/index.d.ts", "require": "./run/index.js", "import": "./run/index.mjs"}}, "files": ["*.js", "*.d.ts", "assert", "parse", "diff", "dist", "run"], "modes": {"diff": "src/diff.js", "assert": "src/assert.js", "default": "src/index.js"}, "scripts": {"build": "bundt", "test": "node test"}, "engines": {"node": ">=8"}, "keywords": ["assert", "diffs", "runner", "snapshot", "test"], "dependencies": {"dequal": "^2.0.0", "diff": "^5.0.0", "kleur": "^4.0.3", "sade": "^1.7.3"}, "devDependencies": {"bundt": "1.1.1", "esm": "3.2.25", "module-alias": "2.2.2", "totalist": "2.0.0"}, "_moduleAliases": {"uvu": "src/index.js", "uvu/diff": "src/diff.js", "uvu/assert": "src/assert.js"}}
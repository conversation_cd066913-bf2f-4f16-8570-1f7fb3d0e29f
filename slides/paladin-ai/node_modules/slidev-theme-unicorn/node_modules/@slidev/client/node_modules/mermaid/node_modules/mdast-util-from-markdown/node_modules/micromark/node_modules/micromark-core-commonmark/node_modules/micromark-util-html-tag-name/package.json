{"name": "micromark-util-html-tag-name", "version": "1.2.0", "description": "micromark utility with list of html tag names", "license": "MIT", "keywords": ["micromark", "util", "utility", "html", "tag", "name"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-html-tag-name", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "exports": "./index.js", "scripts": {}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}
/** @type {Construct} */
export const list: Construct
export type Code = import('micromark-util-types').Code
export type Construct = import('micromark-util-types').Construct
export type ContainerState = import('micromark-util-types').ContainerState
export type Exiter = import('micromark-util-types').Exiter
export type State = import('micromark-util-types').State
export type TokenizeContext = import('micromark-util-types').TokenizeContext
export type Tokenizer = import('micromark-util-types').Tokenizer

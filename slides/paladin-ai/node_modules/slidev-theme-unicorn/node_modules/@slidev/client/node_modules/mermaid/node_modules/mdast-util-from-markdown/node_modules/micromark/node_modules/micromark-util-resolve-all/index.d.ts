/**
 * @typedef {import('micromark-util-types').Event} Event
 * @typedef {import('micromark-util-types').Resolver} Resolver
 * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext
 */
/**
 * Call all `resolveAll`s.
 *
 * @param {Array<{resolveAll?: Resolver | undefined}>} constructs
 *   List of constructs, optionally with `resolveAll`s.
 * @param {Array<Event>} events
 *   List of events.
 * @param {TokenizeContext} context
 *   Context used by `tokenize`.
 * @returns {Array<Event>}
 *   Changed events.
 */
export function resolveAll(
  constructs: Array<{
    resolveAll?: Resolver | undefined
  }>,
  events: Array<import('micromark-util-types').Event>,
  context: TokenizeContext
): Array<import('micromark-util-types').Event>
export type Event = import('micromark-util-types').Event
export type Resolver = import('micromark-util-types').Resolver
export type TokenizeContext = import('micromark-util-types').TokenizeContext

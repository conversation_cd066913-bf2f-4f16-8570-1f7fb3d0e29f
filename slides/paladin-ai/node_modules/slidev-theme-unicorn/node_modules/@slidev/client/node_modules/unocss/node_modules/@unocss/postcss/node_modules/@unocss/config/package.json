{"name": "@unocss/config", "version": "0.58.9", "description": "Config loader for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/config#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/config"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "dependencies": {"unconfig": "^0.3.11", "@unocss/core": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
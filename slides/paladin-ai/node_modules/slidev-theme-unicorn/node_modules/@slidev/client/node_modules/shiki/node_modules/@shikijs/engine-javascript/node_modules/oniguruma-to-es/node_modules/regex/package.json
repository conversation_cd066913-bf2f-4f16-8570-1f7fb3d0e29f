{"name": "regex", "version": "5.1.1", "description": "Regex template tag with extended syntax, context-aware interpolation, and always-on best practices", "author": "<PERSON>", "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./dist/esm/regex.d.ts", "default": "./dist/esm/regex.js"}, "require": {"types": "./dist/cjs/regex.d.ts", "default": "./dist/cjs/regex.js"}}, "./internals": {"types": "./dist/esm/internals.d.ts", "import": "./src/internals.js"}}, "browser": "./dist/regex.min.js", "main": "./dist/cjs/regex.js", "types": "./dist/cjs/regex.d.ts", "scripts": {"bundle:global": "esbuild src/regex.js --global-name=Regex --bundle --minify --sourcemap --outfile=dist/regex.min.js", "bundle:esm": "esbuild src/regex.js --format=esm --bundle --sourcemap --outfile=dist/esm/regex.js", "bundle:cjs": "esbuild src/regex.js --format=cjs --bundle --sourcemap --outfile=dist/cjs/regex.js", "types": "tsc src/regex.js src/internals.js --rootDir src --declaration --allowJs --emitDeclarationOnly --outDir types", "prebuild": "rm -rf dist/* types/*", "build": "pnpm run bundle:global && pnpm run bundle:esm && pnpm run bundle:cjs && pnpm run types", "postbuild": "node scripts/postbuild.js", "pretest": "pnpm run build", "test": "jasmine && tsc --project spec/types/tsconfig.test.json && attw --pack . --entrypoints .", "prepublishOnly": "pnpm test"}, "files": ["dist", "src"], "repository": {"type": "git", "url": "git+https://github.com/slevithan/regex.git"}, "keywords": ["regex", "regexp"], "dependencies": {"regex-utilities": "^2.3.0"}, "devDependencies": {"@arethetypeswrong/cli": "~0.17.2", "esbuild": "^0.24.2", "expect-type": "^1.1.0", "jasmine": "^5.5.0", "typescript": "~5.7.2"}}
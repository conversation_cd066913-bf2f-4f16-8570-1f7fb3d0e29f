{"name": "@shikijs/engine-oniguruma", "type": "module", "version": "1.29.2", "description": "Engine for Shi<PERSON> using Oniguruma RegExp engine in WebAssembly", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/engine-oniguruma"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "shiki-engine", "<PERSON><PERSON><PERSON><PERSON>"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "./wasm-inlined": {"types": "./dist/wasm-inlined.d.mts", "default": "./dist/wasm-inlined.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "typesVersions": {"*": {"wasm-inlined": ["./dist/wasm-inlined.d.mts"], "*": ["./dist/*", "./*"]}}, "files": ["dist"], "dependencies": {"@shikijs/vscode-textmate": "^10.0.1", "@shikijs/types": "1.29.2"}, "devDependencies": {"vscode-oniguruma": "1.7.0"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -cw"}}
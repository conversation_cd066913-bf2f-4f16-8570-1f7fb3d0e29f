{"name": "@unocss/extractor-arbitrary-variants", "version": "0.58.9", "description": "Extractor arbitrary variants for utilities", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/extractor-arbitrary-variants#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/extractor-arbitrary-variants"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-extractor"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@unocss/core": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
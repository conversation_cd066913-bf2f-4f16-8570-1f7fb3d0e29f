{"name": "@unocss/preset-mini", "version": "0.58.9", "description": "The minimal preset for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/preset-mini#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/preset-mini"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./theme": {"types": "./dist/theme.d.ts", "import": "./dist/theme.mjs", "require": "./dist/theme.cjs"}, "./variants": {"types": "./dist/variants.d.ts", "import": "./dist/variants.mjs", "require": "./dist/variants.cjs"}, "./rules": {"types": "./dist/rules.d.ts", "import": "./dist/rules.mjs", "require": "./dist/rules.cjs"}, "./colors": {"types": "./dist/colors.d.ts", "import": "./dist/colors.mjs", "require": "./dist/colors.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["*.css", "*.d.ts", "dist"], "dependencies": {"@unocss/core": "0.58.9", "@unocss/extractor-arbitrary-variants": "0.58.9", "@unocss/rule-utils": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
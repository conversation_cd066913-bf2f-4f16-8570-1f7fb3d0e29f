{"name": "@unocss/preset-icons", "version": "0.58.9", "description": "Pure CSS Icons for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/preset-icons#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/preset-icons"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset", "icons", "css-icons", "iconify"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./browser": {"types": "./dist/browser.d.ts", "import": "./dist/browser.mjs", "require": "./dist/browser.cjs"}, "./core": {"types": "./dist/core.d.ts", "import": "./dist/core.mjs", "require": "./dist/core.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["*.css", "dist"], "dependencies": {"@iconify/utils": "^2.1.22", "ofetch": "^1.3.4", "@unocss/core": "0.58.9"}, "devDependencies": {"@iconify/types": "^2.0.0"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
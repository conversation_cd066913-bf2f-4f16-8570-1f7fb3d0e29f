{"name": "@unocss/preset-web-fonts", "version": "0.58.9", "description": "Web Fonts support for Uno CSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/preset-web-fonts#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/preset-web-fonts"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset", "fonts", "web-fonts", "google-fonts"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["*.css", "dist"], "dependencies": {"ofetch": "^1.3.4", "@unocss/core": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
{"name": "ohash", "version": "1.1.6", "description": "Super fast hashing library based on murmurhash3 written in Vanilla JS", "repository": "unjs/ohash", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts", "require": "./dist/index.cjs"}, "./utils": {"import": "./dist/utils.mjs", "types": "./dist/utils.d.ts", "require": "./dist/utils.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "unbuild", "release": "pnpm test && changelogen --release --push --publish --publishTag 1x", "benchmark": "node benchmark/object-hash.mjs", "test": "pnpm lint && vitest run && pnpm typecheck", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/node": "^22.13.8", "@vitest/coverage-v8": "^3.0.7", "benchmark": "^2.1.4", "changelogen": "^0.6.0", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "prettier": "^3.5.2", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vitest": "^3.0.7"}, "packageManager": "pnpm@10.5.2"}
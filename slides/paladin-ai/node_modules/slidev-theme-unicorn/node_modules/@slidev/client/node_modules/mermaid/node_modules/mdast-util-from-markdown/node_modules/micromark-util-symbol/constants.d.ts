export namespace constants {
  const attentionSideBefore: 1
  const attentionSideAfter: 2
  const atxHeadingOpeningFenceSizeMax: 6
  const autolinkDomainSizeMax: 63
  const autolinkSchemeSizeMax: 32
  const cdataOpeningString: 'CDATA['
  const characterGroupWhitespace: 1
  const characterGroupPunctuation: 2
  const characterReferenceDecimalSizeMax: 7
  const characterReferenceHexadecimalSizeMax: 6
  const characterReferenceNamedSizeMax: 31
  const codeFencedSequenceSizeMin: 3
  const contentTypeDocument: 'document'
  const contentTypeFlow: 'flow'
  const contentTypeContent: 'content'
  const contentTypeString: 'string'
  const contentTypeText: 'text'
  const hardBreakPrefixSizeMin: 2
  const htmlRaw: 1
  const htmlComment: 2
  const htmlInstruction: 3
  const htmlDeclaration: 4
  const htmlCdata: 5
  const htmlBasic: 6
  const htmlComplete: 7
  const htmlRawSizeMax: 8
  const linkResourceDestinationBalanceMax: 32
  const linkReferenceSizeMax: 999
  const listItemValueSizeMax: 10
  const numericBaseDecimal: 10
  const numericBaseHexadecimal: 16
  const tabSize: 4
  const thematicBreakMarkerCountMin: 3
  const v8MaxSafeChunkSize: 10000
}

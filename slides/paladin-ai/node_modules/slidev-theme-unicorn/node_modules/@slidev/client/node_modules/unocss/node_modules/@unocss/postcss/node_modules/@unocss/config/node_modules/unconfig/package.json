{"name": "unconfig", "type": "module", "version": "0.3.13", "packageManager": "pnpm@8.15.6", "description": "A universal solution for loading configurations.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unconfig#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/unconfig.git"}, "bugs": {"url": "https://github.com/antfu/unconfig/issues"}, "keywords": ["config"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./presets": {"types": "./dist/presets.d.ts", "import": "./dist/presets.mjs", "require": "./dist/presets.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"@antfu/utils": "^0.7.7", "defu": "^6.1.4", "jiti": "^1.21.0"}, "devDependencies": {"@antfu/eslint-config": "^2.13.3", "@antfu/ni": "^0.21.12", "@types/node": "^20.12.7", "bumpp": "^9.4.0", "eslint": "^9.0.0", "esno": "^4.7.0", "lodash-es": "^4.17.21", "typescript": "^5.4.5", "unbuild": "^2.0.0", "vitest": "^1.5.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "release": "bumpp --commit --push --tag && pnpm publish", "start": "esno src/index.ts", "test": "vitest"}}
{"name": "micromark-util-resolve-all", "version": "1.1.0", "description": "micromark utility to resolve subtokens", "license": "MIT", "keywords": ["micromark", "util", "utility", "resolve"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-resolve-all", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "exports": "./index.js", "dependencies": {"micromark-util-types": "^1.0.0"}, "scripts": {}, "xo": false, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true}}
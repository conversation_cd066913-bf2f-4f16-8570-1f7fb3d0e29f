{"name": "@unocss/astro", "version": "0.58.9", "description": "UnoCSS integration for Astro", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/astro"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "astro", "astro-integration", "astro-component", "css", "ui"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "peerDependencies": {"vite": "^2.9.0 || ^3.0.0-0 || ^4.0.0 || ^5.0.0-0"}, "peerDependenciesMeta": {"vite": {"optional": true}}, "dependencies": {"@unocss/reset": "0.58.9", "@unocss/core": "0.58.9", "@unocss/vite": "0.58.9"}, "devDependencies": {"astro": "^4.5.12"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
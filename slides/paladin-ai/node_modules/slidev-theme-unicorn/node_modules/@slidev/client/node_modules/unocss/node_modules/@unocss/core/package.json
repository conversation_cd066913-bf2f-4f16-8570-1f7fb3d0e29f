{"name": "@unocss/core", "version": "0.58.9", "description": "The instant on-demand Atomic CSS engine.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/core#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/core"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "atomic-css", "atomic-css-engine", "css", "tailwind", "windicss"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "devDependencies": {"magic-string": "^0.30.8", "unconfig": "^0.3.11"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
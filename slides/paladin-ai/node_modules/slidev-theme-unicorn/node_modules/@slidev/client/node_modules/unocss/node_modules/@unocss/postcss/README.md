# @unocss/postcss

PostCSS plugin for UnoCSS. Supports `@apply`、`@screen` and `theme()` directives.

> **Warning**: Experimental
> This package is in an experimental state right now. It doesn't follow semver, and may introduce breaking changes in patch versions.

## Documentation

Please refer to the [documentation](https://unocss.dev/integrations/postcss).

## License

MIT License &copy; 2022-PRESENT [hannoeru](https://github.com/hannoeru) [sibbng](https://github.com/sibbng)

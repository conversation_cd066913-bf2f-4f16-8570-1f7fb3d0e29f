{"name": "oniguruma-to-es", "version": "2.3.0", "description": "Convert Oniguruma patterns to native JavaScript RegExp", "author": "<PERSON>", "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "browser": "./dist/index.min.js", "main": "./dist/cjs/index.js", "types": "./dist/cjs/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/slevithan/oniguruma-to-es.git"}, "keywords": ["regex", "regexp", "<PERSON><PERSON><PERSON><PERSON>", "textmate-grammar", "transpiler"], "dependencies": {"emoji-regex-xs": "^1.0.0", "regex": "^5.1.1", "regex-recursion": "^5.1.1"}, "devDependencies": {"esbuild": "^0.24.2", "jasmine": "^5.5.0", "typescript": "^5.7.3", "vscode-oniguruma": "^2.0.1"}, "scripts": {"bundle:global": "esbuild src/index.js --global-name=OnigurumaToES --bundle --minify --sourcemap --outfile=dist/index.min.js", "bundle:esm": "esbuild src/index.js --format=esm --bundle --sourcemap --external:emoji-regex-xs --external:regex --external:regex-recursion --outfile=dist/esm/index.js", "bundle:cjs": "esbuild src/index.js --format=cjs --bundle --sourcemap --outfile=dist/cjs/index.js", "types": "tsc src/index.js --rootDir src --declaration --allowJs --emitDeclarationOnly --outDir types", "prebuild": "rm -rf dist/* types/*", "build": "pnpm run bundle:global && pnpm run bundle:esm && pnpm run bundle:cjs && pnpm run types", "postbuild": "node scripts/postbuild.js", "pretest": "pnpm run build", "test": "jasmine", "onig:compare": "node scripts/onig-compare.js", "onig:match": "node scripts/onig-match.js"}}
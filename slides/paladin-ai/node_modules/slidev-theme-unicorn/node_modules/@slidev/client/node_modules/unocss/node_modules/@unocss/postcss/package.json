{"name": "@unocss/postcss", "version": "0.58.9", "description": "PostCSS plugin for UnoCSS", "author": "sibbng <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/postcss#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/postcss"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "peerDependencies": {"postcss": "^8.4.21"}, "dependencies": {"css-tree": "^2.3.1", "fast-glob": "^3.3.2", "magic-string": "^0.30.8", "postcss": "^8.4.38", "@unocss/config": "0.58.9", "@unocss/core": "0.58.9", "@unocss/rule-utils": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
{"name": "@unocss/preset-tagify", "version": "0.58.9", "description": "Tagify preset for UnoCSS", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/preset-tagify#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/preset-tagify"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": ["unocss", "unocss-preset"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "dependencies": {"@unocss/core": "0.58.9"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}
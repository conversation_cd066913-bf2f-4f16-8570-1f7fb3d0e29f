{"name": "@shikijs/langs", "type": "module", "version": "1.29.2", "description": "TextMate grammars for Shiki in ESM", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/langs"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "textmate-grammars"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./abap": "./dist/abap.mjs", "./actionscript-3": "./dist/actionscript-3.mjs", "./ada": "./dist/ada.mjs", "./angular-expression": "./dist/angular-expression.mjs", "./angular-html": "./dist/angular-html.mjs", "./angular-inline-style": "./dist/angular-inline-style.mjs", "./angular-inline-template": "./dist/angular-inline-template.mjs", "./angular-let-declaration": "./dist/angular-let-declaration.mjs", "./angular-template-blocks": "./dist/angular-template-blocks.mjs", "./angular-template": "./dist/angular-template.mjs", "./angular-ts": "./dist/angular-ts.mjs", "./apache": "./dist/apache.mjs", "./apex": "./dist/apex.mjs", "./apl": "./dist/apl.mjs", "./applescript": "./dist/applescript.mjs", "./ara": "./dist/ara.mjs", "./adoc": "./dist/adoc.mjs", "./asciidoc": "./dist/asciidoc.mjs", "./asm": "./dist/asm.mjs", "./astro": "./dist/astro.mjs", "./awk": "./dist/awk.mjs", "./ballerina": "./dist/ballerina.mjs", "./batch": "./dist/batch.mjs", "./bat": "./dist/bat.mjs", "./beancount": "./dist/beancount.mjs", "./be": "./dist/be.mjs", "./berry": "./dist/berry.mjs", "./bibtex": "./dist/bibtex.mjs", "./bicep": "./dist/bicep.mjs", "./blade": "./dist/blade.mjs", "./1c": "./dist/1c.mjs", "./bsl": "./dist/bsl.mjs", "./c": "./dist/c.mjs", "./cdc": "./dist/cdc.mjs", "./cadence": "./dist/cadence.mjs", "./cairo": "./dist/cairo.mjs", "./clarity": "./dist/clarity.mjs", "./clj": "./dist/clj.mjs", "./clojure": "./dist/clojure.mjs", "./cmake": "./dist/cmake.mjs", "./cobol": "./dist/cobol.mjs", "./codeowners": "./dist/codeowners.mjs", "./ql": "./dist/ql.mjs", "./codeql": "./dist/codeql.mjs", "./coffeescript": "./dist/coffeescript.mjs", "./coffee": "./dist/coffee.mjs", "./lisp": "./dist/lisp.mjs", "./common-lisp": "./dist/common-lisp.mjs", "./coq": "./dist/coq.mjs", "./cpp-macro": "./dist/cpp-macro.mjs", "./cpp": "./dist/cpp.mjs", "./crystal": "./dist/crystal.mjs", "./cs": "./dist/cs.mjs", "./csharp": "./dist/csharp.mjs", "./css": "./dist/css.mjs", "./csv": "./dist/csv.mjs", "./cue": "./dist/cue.mjs", "./cql": "./dist/cql.mjs", "./cypher": "./dist/cypher.mjs", "./d": "./dist/d.mjs", "./dart": "./dist/dart.mjs", "./dax": "./dist/dax.mjs", "./desktop": "./dist/desktop.mjs", "./diff": "./dist/diff.mjs", "./dockerfile": "./dist/dockerfile.mjs", "./docker": "./dist/docker.mjs", "./dotenv": "./dist/dotenv.mjs", "./dream-maker": "./dist/dream-maker.mjs", "./edge": "./dist/edge.mjs", "./elixir": "./dist/elixir.mjs", "./elm": "./dist/elm.mjs", "./elisp": "./dist/elisp.mjs", "./emacs-lisp": "./dist/emacs-lisp.mjs", "./erb": "./dist/erb.mjs", "./erl": "./dist/erl.mjs", "./erlang": "./dist/erlang.mjs", "./es-tag-css": "./dist/es-tag-css.mjs", "./es-tag-glsl": "./dist/es-tag-glsl.mjs", "./es-tag-html": "./dist/es-tag-html.mjs", "./es-tag-sql": "./dist/es-tag-sql.mjs", "./es-tag-xml": "./dist/es-tag-xml.mjs", "./fennel": "./dist/fennel.mjs", "./fish": "./dist/fish.mjs", "./ftl": "./dist/ftl.mjs", "./fluent": "./dist/fluent.mjs", "./f": "./dist/f.mjs", "./for": "./dist/for.mjs", "./f77": "./dist/f77.mjs", "./fortran-fixed-form": "./dist/fortran-fixed-form.mjs", "./f90": "./dist/f90.mjs", "./f95": "./dist/f95.mjs", "./f03": "./dist/f03.mjs", "./f08": "./dist/f08.mjs", "./f18": "./dist/f18.mjs", "./fortran-free-form": "./dist/fortran-free-form.mjs", "./fs": "./dist/fs.mjs", "./fsharp": "./dist/fsharp.mjs", "./gdresource": "./dist/gdresource.mjs", "./gdscript": "./dist/gdscript.mjs", "./gdshader": "./dist/gdshader.mjs", "./genie": "./dist/genie.mjs", "./gherkin": "./dist/gherkin.mjs", "./git-commit": "./dist/git-commit.mjs", "./git-rebase": "./dist/git-rebase.mjs", "./gleam": "./dist/gleam.mjs", "./gjs": "./dist/gjs.mjs", "./glimmer-js": "./dist/glimmer-js.mjs", "./gts": "./dist/gts.mjs", "./glimmer-ts": "./dist/glimmer-ts.mjs", "./glsl": "./dist/glsl.mjs", "./gnuplot": "./dist/gnuplot.mjs", "./go": "./dist/go.mjs", "./gql": "./dist/gql.mjs", "./graphql": "./dist/graphql.mjs", "./groovy": "./dist/groovy.mjs", "./hack": "./dist/hack.mjs", "./haml": "./dist/haml.mjs", "./hbs": "./dist/hbs.mjs", "./handlebars": "./dist/handlebars.mjs", "./hs": "./dist/hs.mjs", "./haskell": "./dist/haskell.mjs", "./haxe": "./dist/haxe.mjs", "./hcl": "./dist/hcl.mjs", "./hjson": "./dist/hjson.mjs", "./hlsl": "./dist/hlsl.mjs", "./html-derivative": "./dist/html-derivative.mjs", "./html": "./dist/html.mjs", "./http": "./dist/http.mjs", "./hxml": "./dist/hxml.mjs", "./hy": "./dist/hy.mjs", "./imba": "./dist/imba.mjs", "./properties": "./dist/properties.mjs", "./ini": "./dist/ini.mjs", "./java": "./dist/java.mjs", "./js": "./dist/js.mjs", "./javascript": "./dist/javascript.mjs", "./jinja-html": "./dist/jinja-html.mjs", "./jinja": "./dist/jinja.mjs", "./jison": "./dist/jison.mjs", "./json": "./dist/json.mjs", "./json5": "./dist/json5.mjs", "./jsonc": "./dist/jsonc.mjs", "./jsonl": "./dist/jsonl.mjs", "./jsonnet": "./dist/jsonnet.mjs", "./fsl": "./dist/fsl.mjs", "./jssm": "./dist/jssm.mjs", "./jsx": "./dist/jsx.mjs", "./jl": "./dist/jl.mjs", "./julia": "./dist/julia.mjs", "./kt": "./dist/kt.mjs", "./kts": "./dist/kts.mjs", "./kotlin": "./dist/kotlin.mjs", "./kql": "./dist/kql.mjs", "./kusto": "./dist/kusto.mjs", "./latex": "./dist/latex.mjs", "./lean4": "./dist/lean4.mjs", "./lean": "./dist/lean.mjs", "./less": "./dist/less.mjs", "./liquid": "./dist/liquid.mjs", "./log": "./dist/log.mjs", "./logo": "./dist/logo.mjs", "./lua": "./dist/lua.mjs", "./luau": "./dist/luau.mjs", "./makefile": "./dist/makefile.mjs", "./make": "./dist/make.mjs", "./markdown-vue": "./dist/markdown-vue.mjs", "./md": "./dist/md.mjs", "./markdown": "./dist/markdown.mjs", "./marko": "./dist/marko.mjs", "./matlab": "./dist/matlab.mjs", "./mdc": "./dist/mdc.mjs", "./mdx": "./dist/mdx.mjs", "./mmd": "./dist/mmd.mjs", "./mermaid": "./dist/mermaid.mjs", "./mips": "./dist/mips.mjs", "./mipsasm": "./dist/mipsasm.mjs", "./mojo": "./dist/mojo.mjs", "./move": "./dist/move.mjs", "./nar": "./dist/nar.mjs", "./narrat": "./dist/narrat.mjs", "./nf": "./dist/nf.mjs", "./nextflow": "./dist/nextflow.mjs", "./nginx": "./dist/nginx.mjs", "./nim": "./dist/nim.mjs", "./nix": "./dist/nix.mjs", "./nu": "./dist/nu.mjs", "./nushell": "./dist/nushell.mjs", "./objc": "./dist/objc.mjs", "./objective-c": "./dist/objective-c.mjs", "./objective-cpp": "./dist/objective-cpp.mjs", "./ocaml": "./dist/ocaml.mjs", "./pascal": "./dist/pascal.mjs", "./perl": "./dist/perl.mjs", "./php": "./dist/php.mjs", "./plsql": "./dist/plsql.mjs", "./pot": "./dist/pot.mjs", "./potx": "./dist/potx.mjs", "./po": "./dist/po.mjs", "./polar": "./dist/polar.mjs", "./postcss": "./dist/postcss.mjs", "./powerquery": "./dist/powerquery.mjs", "./ps": "./dist/ps.mjs", "./ps1": "./dist/ps1.mjs", "./powershell": "./dist/powershell.mjs", "./prisma": "./dist/prisma.mjs", "./prolog": "./dist/prolog.mjs", "./protobuf": "./dist/protobuf.mjs", "./proto": "./dist/proto.mjs", "./jade": "./dist/jade.mjs", "./pug": "./dist/pug.mjs", "./puppet": "./dist/puppet.mjs", "./purescript": "./dist/purescript.mjs", "./py": "./dist/py.mjs", "./python": "./dist/python.mjs", "./qml": "./dist/qml.mjs", "./qmldir": "./dist/qmldir.mjs", "./qss": "./dist/qss.mjs", "./r": "./dist/r.mjs", "./racket": "./dist/racket.mjs", "./perl6": "./dist/perl6.mjs", "./raku": "./dist/raku.mjs", "./razor": "./dist/razor.mjs", "./reg": "./dist/reg.mjs", "./regex": "./dist/regex.mjs", "./regexp": "./dist/regexp.mjs", "./rel": "./dist/rel.mjs", "./riscv": "./dist/riscv.mjs", "./rst": "./dist/rst.mjs", "./rb": "./dist/rb.mjs", "./ruby": "./dist/ruby.mjs", "./rs": "./dist/rs.mjs", "./rust": "./dist/rust.mjs", "./sas": "./dist/sas.mjs", "./sass": "./dist/sass.mjs", "./scala": "./dist/scala.mjs", "./scheme": "./dist/scheme.mjs", "./scss": "./dist/scss.mjs", "./1c-query": "./dist/1c-query.mjs", "./sdbl": "./dist/sdbl.mjs", "./shader": "./dist/shader.mjs", "./shaderlab": "./dist/shaderlab.mjs", "./bash": "./dist/bash.mjs", "./sh": "./dist/sh.mjs", "./shell": "./dist/shell.mjs", "./zsh": "./dist/zsh.mjs", "./shellscript": "./dist/shellscript.mjs", "./console": "./dist/console.mjs", "./shellsession": "./dist/shellsession.mjs", "./smalltalk": "./dist/smalltalk.mjs", "./solidity": "./dist/solidity.mjs", "./closure-templates": "./dist/closure-templates.mjs", "./soy": "./dist/soy.mjs", "./sparql": "./dist/sparql.mjs", "./spl": "./dist/spl.mjs", "./splunk": "./dist/splunk.mjs", "./sql": "./dist/sql.mjs", "./ssh-config": "./dist/ssh-config.mjs", "./stata": "./dist/stata.mjs", "./styl": "./dist/styl.mjs", "./stylus": "./dist/stylus.mjs", "./svelte": "./dist/svelte.mjs", "./swift": "./dist/swift.mjs", "./system-verilog": "./dist/system-verilog.mjs", "./systemd": "./dist/systemd.mjs", "./talon": "./dist/talon.mjs", "./talonscript": "./dist/talonscript.mjs", "./tasl": "./dist/tasl.mjs", "./tcl": "./dist/tcl.mjs", "./templ": "./dist/templ.mjs", "./tf": "./dist/tf.mjs", "./tfvars": "./dist/tfvars.mjs", "./terraform": "./dist/terraform.mjs", "./tex": "./dist/tex.mjs", "./toml": "./dist/toml.mjs", "./lit": "./dist/lit.mjs", "./ts-tags": "./dist/ts-tags.mjs", "./tsv": "./dist/tsv.mjs", "./tsx": "./dist/tsx.mjs", "./turtle": "./dist/turtle.mjs", "./twig": "./dist/twig.mjs", "./ts": "./dist/ts.mjs", "./typescript": "./dist/typescript.mjs", "./tsp": "./dist/tsp.mjs", "./typespec": "./dist/typespec.mjs", "./typ": "./dist/typ.mjs", "./typst": "./dist/typst.mjs", "./v": "./dist/v.mjs", "./vala": "./dist/vala.mjs", "./cmd": "./dist/cmd.mjs", "./vb": "./dist/vb.mjs", "./verilog": "./dist/verilog.mjs", "./vhdl": "./dist/vhdl.mjs", "./vim": "./dist/vim.mjs", "./vimscript": "./dist/vimscript.mjs", "./viml": "./dist/viml.mjs", "./vue-directives": "./dist/vue-directives.mjs", "./vue-html": "./dist/vue-html.mjs", "./vue-interpolations": "./dist/vue-interpolations.mjs", "./vue-sfc-style-variable-injection": "./dist/vue-sfc-style-variable-injection.mjs", "./vue": "./dist/vue.mjs", "./vy": "./dist/vy.mjs", "./vyper": "./dist/vyper.mjs", "./wasm": "./dist/wasm.mjs", "./wenyan": "./dist/wenyan.mjs", "./wgsl": "./dist/wgsl.mjs", "./mediawiki": "./dist/mediawiki.mjs", "./wiki": "./dist/wiki.mjs", "./wikitext": "./dist/wikitext.mjs", "./wl": "./dist/wl.mjs", "./wolfram": "./dist/wolfram.mjs", "./xml": "./dist/xml.mjs", "./xsl": "./dist/xsl.mjs", "./yml": "./dist/yml.mjs", "./yaml": "./dist/yaml.mjs", "./zenscript": "./dist/zenscript.mjs", "./zig": "./dist/zig.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/types": "1.29.2"}, "devDependencies": {"tm-grammars": "^1.22.8"}, "scripts": {"build": "pnpm prepare"}}
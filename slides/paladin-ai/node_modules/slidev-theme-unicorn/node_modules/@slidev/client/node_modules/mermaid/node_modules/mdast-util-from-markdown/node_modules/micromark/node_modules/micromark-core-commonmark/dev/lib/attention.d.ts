/** @type {Construct} */
export const attention: Construct
export type Code = import('micromark-util-types').Code
export type Construct = import('micromark-util-types').Construct
export type Event = import('micromark-util-types').Event
export type Point = import('micromark-util-types').Point
export type Resolver = import('micromark-util-types').Resolver
export type State = import('micromark-util-types').State
export type Token = import('micromark-util-types').Token
export type TokenizeContext = import('micromark-util-types').TokenizeContext
export type Tokenizer = import('micromark-util-types').Tokenizer

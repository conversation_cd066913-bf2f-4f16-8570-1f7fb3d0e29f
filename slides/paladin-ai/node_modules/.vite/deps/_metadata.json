{"hash": "71b4c5e7", "configHash": "3d9f1940", "lockfileHash": "13fb63b5", "browserHash": "795a1165", "optimized": {"@slidev/cli > @slidev/client > @typescript/ata": {"src": "../../@typescript/ata/dist/index.js", "file": "@slidev_cli___@slidev_client___@typescript_ata.js", "fileHash": "e83072bb", "needsInterop": false}, "@slidev/cli > @slidev/client > file-saver": {"src": "../../file-saver/dist/FileSaver.min.js", "file": "@slidev_cli___@slidev_client___file-saver.js", "fileHash": "bed99d0e", "needsInterop": true}, "@slidev/cli > @slidev/client > lz-string": {"src": "../../lz-string/libs/lz-string.js", "file": "@slidev_cli___@slidev_client___lz-string.js", "fileHash": "0d739391", "needsInterop": true}, "@slidev/cli > @slidev/client > prettier": {"src": "../../prettier/standalone.mjs", "file": "@slidev_cli___@slidev_client___prettier.js", "fileHash": "0646e0a1", "needsInterop": false}, "@slidev/cli > @slidev/client > recordrtc": {"src": "../../recordrtc/RecordRTC.js", "file": "@slidev_cli___@slidev_client___recordrtc.js", "fileHash": "30dd1000", "needsInterop": true}, "@slidev/cli > @slidev/client > typescript": {"src": "../../typescript/lib/typescript.js", "file": "@slidev_cli___@slidev_client___typescript.js", "fileHash": "7f6c2d61", "needsInterop": true}, "@slidev/cli > @slidev/client > yaml": {"src": "../../yaml/browser/index.js", "file": "@slidev_cli___@slidev_client___yaml.js", "fileHash": "03422196", "needsInterop": false}, "file-saver": {"src": "../../file-saver/dist/FileSaver.min.js", "file": "file-saver.js", "fileHash": "25f9ab8e", "needsInterop": true}, "nanotar": {"src": "../../nanotar/dist/index.mjs", "file": "nanotar.js", "fileHash": "81c0ccc3", "needsInterop": false}, "pptxgenjs": {"src": "../../pptxgenjs/dist/pptxgen.es.js", "file": "pptxgenjs.js", "fileHash": "451b16da", "needsInterop": false}, "recordrtc": {"src": "../../recordrtc/RecordRTC.js", "file": "recordrtc.js", "fileHash": "a212c497", "needsInterop": true}}, "chunks": {"node_fs-YNKJHPX4": {"file": "node_fs-YNKJHPX4.js"}, "node_https-HKDKIPSF": {"file": "node_https-HKDKIPSF.js"}, "chunk-FZPG3NK2": {"file": "chunk-FZPG3NK2.js"}, "chunk-2KYM5DNG": {"file": "chunk-2KYM5DNG.js"}, "chunk-PR4QN5HX": {"file": "chunk-PR4QN5HX.js"}}}
{"hash": "b2da1b45", "configHash": "8ce7a32f", "lockfileHash": "13fb63b5", "browserHash": "e0952e11", "optimized": {"@slidev/cli > @slidev/client > @typescript/ata": {"src": "../../@typescript/ata/dist/index.js", "file": "@slidev_cli___@slidev_client___@typescript_ata.js", "fileHash": "94d227ef", "needsInterop": false}, "@slidev/cli > @slidev/client > file-saver": {"src": "../../file-saver/dist/FileSaver.min.js", "file": "@slidev_cli___@slidev_client___file-saver.js", "fileHash": "ecea7b78", "needsInterop": true}, "@slidev/cli > @slidev/client > lz-string": {"src": "../../lz-string/libs/lz-string.js", "file": "@slidev_cli___@slidev_client___lz-string.js", "fileHash": "cb3fe7cc", "needsInterop": true}, "@slidev/cli > @slidev/client > prettier": {"src": "../../prettier/standalone.mjs", "file": "@slidev_cli___@slidev_client___prettier.js", "fileHash": "99996587", "needsInterop": false}, "@slidev/cli > @slidev/client > recordrtc": {"src": "../../recordrtc/RecordRTC.js", "file": "@slidev_cli___@slidev_client___recordrtc.js", "fileHash": "4faa09c8", "needsInterop": true}, "@slidev/cli > @slidev/client > typescript": {"src": "../../typescript/lib/typescript.js", "file": "@slidev_cli___@slidev_client___typescript.js", "fileHash": "74762e22", "needsInterop": true}, "@slidev/cli > @slidev/client > yaml": {"src": "../../yaml/browser/index.js", "file": "@slidev_cli___@slidev_client___yaml.js", "fileHash": "fa82f166", "needsInterop": false}, "file-saver": {"src": "../../file-saver/dist/FileSaver.min.js", "file": "file-saver.js", "fileHash": "5bb33718", "needsInterop": true}, "nanotar": {"src": "../../nanotar/dist/index.mjs", "file": "nanotar.js", "fileHash": "814b906f", "needsInterop": false}, "pptxgenjs": {"src": "../../pptxgenjs/dist/pptxgen.es.js", "file": "pptxgenjs.js", "fileHash": "65edc270", "needsInterop": false}, "recordrtc": {"src": "../../recordrtc/RecordRTC.js", "file": "recordrtc.js", "fileHash": "32b489b6", "needsInterop": true}}, "chunks": {"node_fs-YNKJHPX4": {"file": "node_fs-YNKJHPX4.js"}, "node_https-HKDKIPSF": {"file": "node_https-HKDKIPSF.js"}, "chunk-FZPG3NK2": {"file": "chunk-FZPG3NK2.js"}, "chunk-2KYM5DNG": {"file": "chunk-2KYM5DNG.js"}, "chunk-PR4QN5HX": {"file": "chunk-PR4QN5HX.js"}}}
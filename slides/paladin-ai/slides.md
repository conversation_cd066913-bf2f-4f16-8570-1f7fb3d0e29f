---
theme: unicorn
colorScheme: 'dark'
layout: intro
background: https://images.unsplash.com/photo-1518709268805-4e9042af2176?q=80&w=1925&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D
title: Paladin-AI
info: |
  ## Paladin-AI
  An Agentic Assistant for Infrastructure Debugging and Analysis
  
  By Barun Debnath
class: text-center
drawings:
  persist: false
transition: slide-left
mdc: true
---

# Paladin-AI

<div class="text-2xl mb-8">
An Agentic Assistant for Infrastructure Debugging and Analysis
</div>

<div class="text-lg mb-8">
By: <span class="text-blue-400">Bar<PERSON></span>
</div>

```
    ____        __          ___          ___    ____
   / __ \____ _/ /___ _____/ (_)___     /   |  /  _/
  / /_/ / __ `/ / __ `/ __  / / __ \   / /| |  / /  
 / ____/ /_/ / / /_/ / /_/ / / / / /  / ___ |_/ /   
/_/    \__,_/_/\__,_/\__,_/_/_/ /_/  /_/  |_/___/   
```

<div class="mt-8 space-y-4 text-left max-w-3xl mx-auto">
  <div class="flex items-center space-x-3">
    <carbon:bot class="text-green-400" />
    <span>An Agentic Assistant to debug/analyze infra issues and queries</span>
  </div>
  
  <div class="flex items-center space-x-3">
    <carbon:application class="text-blue-400" />
    <span>Available as: CLI, API, Chat Based web app, Discord bot</span>
  </div>
  
  <div class="flex items-center space-x-3">
    <carbon:integration class="text-purple-400" />
    <span>Integrates: Prometheus, Alertmanager, and Loki</span>
  </div>
</div>

<div class="abs-br m-6 text-xl">
  <a href="https://github.com/d-cryptic/paladin-ai" target="_blank" class="slidev-icon-btn">
    <carbon:logo-github />
  </a>
</div>

<!--
Paladin-AI is an intelligent infrastructure assistant that helps SREs and DevOps teams debug and analyze infrastructure issues using AI-powered workflows.
-->

---
layout: two-cols
layoutClass: gap-16
---

# Demo

<div class="space-y-6">

## <carbon:chat class="inline text-blue-400" /> Chat App
<div class="ml-6 space-y-2">
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Query, Action and Incident Flow</span>
  </div>
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Memory/instructions and Documentation as context</span>
  </div>
</div>

## <carbon:logo-discord class="inline text-purple-400" /> Discord MCP
<div class="ml-6 space-y-2">
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Auto Alert Analysis</span>
  </div>
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Chat and discussions as memory</span>
  </div>
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Discord Bot</span>
  </div>
</div>

## <carbon:network-3 class="inline text-green-400" /> Neo4j Memory Graph
<div class="ml-6 space-y-2">
  <div class="flex items-center space-x-2">
    <input type="checkbox" disabled />
    <span>Dynamic relationship mapping</span>
  </div>
</div>

</div>

::right::

<div class="h-full flex flex-col justify-center">

```mermaid {theme: 'dark', scale: 0.8}
graph TD
    A[User Input] --> B{Categorizer}
    B -->|Query| C[Query Workflow]
    B -->|Action| D[Action Workflow]
    B -->|Incident| E[Incident Workflow]
    
    C --> F[Prometheus Tools]
    D --> G[Alertmanager Tools]
    E --> H[Loki Tools]
    
    F --> I[Memory System]
    G --> I
    H --> I
    
    I --> J[Neo4j Graph]
    I --> K[Vector Store]
    
    style A fill:#4ade80
    style B fill:#f59e0b
    style I fill:#8b5cf6
```

<div class="mt-4 text-sm opacity-75">
Workflow Architecture Overview
</div>

</div>

<!--
The demo showcases three main components: the chat application with different workflow types, Discord integration for automated alert analysis, and the Neo4j memory graph for intelligent context retention.
-->

---
layout: default
---

# How is it useful for SRE?

<div class="grid grid-cols-2 gap-8 mt-8">

<div class="space-y-6">

## <carbon:time class="inline text-green-400" /> Faster Incident Response
<div class="text-sm opacity-80 ml-6">
Automatically analyzes alerts and logs to provide immediate insights, reducing MTTR from hours to minutes through intelligent correlation of metrics and events.
</div>

## <carbon:brain class="inline text-blue-400" /> Intelligent Context Retention
<div class="text-sm opacity-80 ml-6">
Learns from past incidents and debugging sessions, building a knowledge graph that helps identify patterns and suggest solutions based on historical data.
</div>

</div>

<div class="space-y-6">

## <carbon:workflow class="inline text-purple-400" /> Automated Workflow Orchestration
<div class="text-sm opacity-80 ml-6">
Streamlines complex debugging processes by automatically selecting appropriate tools and executing multi-step analysis workflows based on query type and context.
</div>

## <carbon:collaborate class="inline text-orange-400" /> Enhanced Team Collaboration
<div class="text-sm opacity-80 ml-6">
Integrates with Discord and chat platforms to provide real-time assistance during incidents, enabling seamless knowledge sharing across team members.
</div>

</div>

</div>

<div class="mt-12 p-4 bg-gray-800 rounded-lg">
<div class="text-center text-lg font-semibold text-blue-400 mb-2">
Key Benefits for SRE Teams
</div>
<div class="grid grid-cols-3 gap-4 text-sm">
  <div class="text-center">
    <carbon:dashboard class="text-2xl mx-auto mb-1 text-green-400" />
    <div>Unified Monitoring</div>
  </div>
  <div class="text-center">
    <carbon:ai-results class="text-2xl mx-auto mb-1 text-blue-400" />
    <div>AI-Powered Analysis</div>
  </div>
  <div class="text-center">
    <carbon:education class="text-2xl mx-auto mb-1 text-purple-400" />
    <div>Continuous Learning</div>
  </div>
</div>
</div>

<!--
Paladin-AI significantly enhances SRE productivity by providing intelligent automation, context-aware assistance, and collaborative debugging capabilities that reduce incident response time and improve system reliability.
-->

---
layout: default
---

# Future Scope

<div class="grid grid-cols-1 gap-6 mt-8">

<div class="p-6 bg-gradient-to-r from-blue-900/30 to-purple-900/30 rounded-lg border border-blue-500/20">
  <div class="flex items-center space-x-3 mb-3">
    <carbon:rocket class="text-2xl text-blue-400" />
    <h3 class="text-xl font-semibold">Performance Optimization</h3>
  </div>
  <p class="text-sm opacity-80">
    Decreasing the latency in tool calling and enhancing accuracy through optimized LLM inference, caching strategies, and parallel processing workflows.
  </p>
</div>

<div class="p-6 bg-gradient-to-r from-green-900/30 to-blue-900/30 rounded-lg border border-green-500/20">
  <div class="flex items-center space-x-3 mb-3">
    <carbon:plug class="text-2xl text-green-400" />
    <h3 class="text-xl font-semibold">MCP Server Integration Engine</h3>
  </div>
  <p class="text-sm opacity-80">
    Create a universal engine to integrate other MCP servers, enabling seamless connectivity with diverse monitoring and observability tools across different platforms.
  </p>
</div>

<div class="p-6 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-lg border border-purple-500/20">
  <div class="flex items-center space-x-3 mb-3">
    <carbon:user-identification class="text-2xl text-purple-400" />
    <h3 class="text-xl font-semibold">Learning-Based User Authentication</h3>
  </div>
  <p class="text-sm opacity-80">
    Implement user authentication with debugging process tracking, enabling the LLM to learn from senior employees' debugging patterns and mentor junior team members.
  </p>
</div>

<div class="grid grid-cols-2 gap-6">
  <div class="p-6 bg-gradient-to-r from-orange-900/30 to-red-900/30 rounded-lg border border-orange-500/20">
    <div class="flex items-center space-x-3 mb-3">
      <carbon:money class="text-2xl text-orange-400" />
      <h3 class="text-lg font-semibold">Cost Analysis & Ops</h3>
    </div>
    <p class="text-sm opacity-80">
      Extend capabilities to cost analysis and other operational workflows for comprehensive infrastructure management.
    </p>
  </div>

  <div class="p-6 bg-gradient-to-r from-teal-900/30 to-cyan-900/30 rounded-lg border border-teal-500/20">
    <div class="flex items-center space-x-3 mb-3">
      <carbon:gui-management class="text-2xl text-teal-400" />
      <h3 class="text-lg font-semibold">Enhanced UI/UX</h3>
    </div>
    <p class="text-sm opacity-80">
      Develop a more intuitive and responsive user interface with advanced visualization and interactive debugging features.
    </p>
  </div>
</div>

</div>

<!--
The future roadmap focuses on performance improvements, extensibility through MCP server integration, intelligent learning systems, and expanded operational capabilities to make Paladin-AI the ultimate SRE companion.
-->

---
layout: center
class: text-center
---

# Thank You

<div class="text-2xl mb-8 text-blue-400">
Questions & Discussion
</div>

<div class="flex justify-center space-x-8 text-4xl">
  <a href="https://github.com/d-cryptic/paladin-ai" target="_blank" class="hover:text-blue-400 transition-colors">
    <carbon:logo-github />
  </a>
  <a href="mailto:<EMAIL>" class="hover:text-green-400 transition-colors">
    <carbon:email />
  </a>
  <a href="https://linkedin.com/in/barun-debnath" target="_blank" class="hover:text-blue-600 transition-colors">
    <carbon:logo-linkedin />
  </a>
</div>

<div class="mt-8 text-lg">
<span class="text-purple-400">Paladin-AI</span> - Your Infrastructure Guardian
</div>

<PoweredBySlidev mt-10 />

<!--
Thank you for your attention. I'm excited to discuss how Paladin-AI can transform your infrastructure debugging and monitoring workflows.
-->
